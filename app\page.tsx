"use client"

import { useState } from "react"
import BannerSlider from "./components/BannerSlider"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import PaginatedProductSection from "./components/PaginatedProductSection"
import { mockProducts, mockBanners, mockHomepageSections } from "./data/mockData"

export default function HomePage() {
  // TODO: Replace with actual user role from Supabase auth
  const userRole = "user"

  // Get active homepage sections sorted by order
  const activeSections = mockHomepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Compact Promotional Banner */}
      <section className="container mx-auto px-4 pt-2 pb-2">
        <PromoBanner />
      </section>

      {/* Main Product Catalog */}
      <div className="container mx-auto px-4 py-4">
        <PaginatedProductSection
          title="🎮 جميع المنتجات"
          products={mockProducts}
          userRole={userRole}
          productsPerPage={20}
        />
      </div>

      {/* Featured Sections (Optional - can be removed if not needed) */}
      <div className="container mx-auto px-4 pb-8">
        {activeSections.slice(0, 2).map((section) => {
          const sectionProducts = mockProducts.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={`${section.emoji || ""} ${section.title}`.trim()}
              products={sectionProducts.slice(0, 8)} // Limit to 8 products for featured sections
              userRole={userRole}
            />
          )
        })}
      </div>
    </div>
  )
}
